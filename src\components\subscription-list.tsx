export function SubscriptionList({ subscriptions }: { subscriptions: any[] }) {
  if (subscriptions.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No active subscriptions
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {subscriptions.map((subscription) => {
        const isActive =
          subscription.isActive && new Date(subscription.endDate) > new Date();

        return (
          <div key={subscription.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">{subscription.creator.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {isActive ? "Active" : "Expired"}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm">
                  {new Date(subscription.startDate).toLocaleDateString()} -
                  {new Date(subscription.endDate).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
