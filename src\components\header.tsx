"use client";

import { TopUpDialog } from "@/components/top-up-dialog";
import { Button } from "@/components/ui/button";
import { useUserStore } from "@/lib/store/useUserStore";
import { signIn, signOut } from "next-auth/react";
import Link from "next/link";
import { useState } from "react";

export function Header() {
  const { user, balance } = useUserStore();
  const [showTopUp, setShowTopUp] = useState(false);

  return (
    <header className="border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold">
            PremiumTube
          </Link>

          <div className="flex items-center gap-4">
            {user ? (
              <>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    Balance: Rp{balance.toLocaleString()}
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowTopUp(true)}
                  >
                    Top Up
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm">Hi, {user.name}</span>
                  <Button size="sm" variant="ghost" onClick={() => signOut()}>
                    Logout
                  </Button>
                </div>
              </>
            ) : (
              <Button onClick={() => signIn("google")}>Sign In</Button>
            )}
          </div>
        </div>
      </div>

      <TopUpDialog open={showTopUp} onOpenChange={setShowTopUp} />
    </header>
  );
}
