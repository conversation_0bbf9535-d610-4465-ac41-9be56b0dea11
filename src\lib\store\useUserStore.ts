import { User } from "@prisma/client";
import { create } from "zustand";

interface UserState {
  user: User | null;
  balance: number;
  setUser: (user: User | null) => void;
  setBalance: (balance: number) => void;
  addBalance: (amount: number) => void;
  deductBalance: (amount: number) => void;
}

export const useUserStore = create<UserState>()((set) => ({
  user: null,
  balance: 0,
  setUser: (user) => set({ user, balance: user?.balance || 0 }),
  setBalance: (balance) => set({ balance }),
  addBalance: (amount) => set((state) => ({ balance: state.balance + amount })),
  deductBalance: (amount) =>
    set((state) => ({ balance: state.balance - amount })),
}));
