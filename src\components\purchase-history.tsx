export function PurchaseHistory({ purchases }: { purchases: any[] }) {
  if (purchases.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No purchase history
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {purchases.map((purchase) => (
        <div key={purchase.id} className="border rounded-lg p-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold">{purchase.video.title}</h3>
              <p className="text-sm text-muted-foreground">
                by {purchase.video.creator.name}
              </p>
            </div>
            <div className="text-right">
              <p className="font-semibold">
                Rp{purchase.amount.toLocaleString()}
              </p>
              <p className="text-sm text-muted-foreground">
                {new Date(purchase.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
