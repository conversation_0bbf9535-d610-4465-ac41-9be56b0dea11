import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { topUpSchema } from "@/lib/schemas";
import { NextResponse } from "next/server";
import { z } from "zod";

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = topUpSchema.parse(body);

    // Update user balance
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        balance: {
          increment: validatedData.amount,
        },
      },
      select: {
        id: true,
        balance: true,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues }, { status: 400 });
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
