// components/video-list.tsx
import { Video } from "@prisma/client";

export function VideoList({ videos }: { videos: Video[] }) {
  if (videos.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No videos uploaded yet
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {videos.map((video) => (
        <div key={video.id} className="border rounded-lg p-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold">{video.title}</h3>
              <p className="text-sm text-muted-foreground mt-1">
                {video.description}
              </p>
              <div className="flex items-center gap-4 mt-2">
                <span className="text-sm">
                  Price: Rp{video.price.toLocaleString()}
                </span>
                <span className="text-sm text-muted-foreground">
                  {new Date(video.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
