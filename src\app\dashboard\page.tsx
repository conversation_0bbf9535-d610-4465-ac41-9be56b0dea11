import { VideoList } from "@/components/video-list";
import { VideoUploadForm } from "@/components/video-upload-form";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export default async function DashboardPage() {
  const session = await auth();

  if (!session?.user?.id) {
    return <div>Please sign in to access dashboard</div>;
  }

  const videos = await prisma.video.findMany({
    where: {
      creatorId: session.user.id,
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Creator Dashboard</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Upload New Video</h2>
          <VideoUploadForm />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Your Videos</h2>
          <VideoList videos={videos} />
        </div>
      </div>
    </div>
  );
}
