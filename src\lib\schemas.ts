import { z } from "zod";

export const videoSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  url: z.string().url("Invalid URL"),
  thumbnail: z.string().url("Invalid thumbnail URL").optional(),
  price: z.number().min(3000, "Minimum price is Rp3000"),
});

export const subscriptionSchema = z.object({
  creatorId: z.string(),
  amount: z.number().min(15999, "Minimum subscription is Rp15999"),
});

export const purchaseSchema = z.object({
  videoId: z.string(),
  amount: z.number().min(3000, "Minimum purchase is Rp3000"),
});

export const topUpSchema = z.object({
  amount: z.number().min(10000, "Minimum top up is Rp10000"),
});
