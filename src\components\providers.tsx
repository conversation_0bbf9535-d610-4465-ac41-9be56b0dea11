"use client";

import { useUserStore } from "@/lib/store/useUserStore";
import { useEffect } from "react";

export function Providers({ children }: { children: React.ReactNode }) {
  const { setUser } = useUserStore();

  useEffect(() => {
    // Initialize user data
    const fetchUserData = async () => {
      try {
        const response = await fetch("/api/user");
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        }
      } catch (error) {
        console.error("Failed to fetch user data:", error);
      }
    };

    fetchUserData();
  }, [setUser]);

  return <>{children}</>;
}
