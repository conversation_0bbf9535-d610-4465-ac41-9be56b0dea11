import { Button } from "@/components/ui/button";
import { VideoPlayer } from "@/components/video-player";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export default async function Home() {
  const session = await auth();
  const videos = await prisma.video.findMany({
    include: {
      creator: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Premium Video Content</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {videos.map((video) => (
          <VideoCard key={video.id} video={video} userId={session?.user?.id} />
        ))}
      </div>
    </div>
  );
}

async function VideoCard({ video, userId }: { video: any; userId?: string }) {
  const isPurchased = userId
    ? await prisma.purchase.findFirst({
        where: {
          userId,
          videoId: video.id,
        },
      })
    : false;

  const isSubscribed = userId
    ? await prisma.subscription.findFirst({
        where: {
          userId,
          creatorId: video.creatorId,
          isActive: true,
          endDate: {
            gte: new Date(),
          },
        },
      })
    : false;

  return (
    <div className="border rounded-lg overflow-hidden">
      <div className="aspect-video bg-gray-200">
        {/* Thumbnail placeholder */}
        {video.thumbnail ? (
          <img
            src={video.thumbnail}
            alt={video.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <span className="text-gray-500">No thumbnail</span>
          </div>
        )}
      </div>

      <div className="p-4">
        <h3 className="font-semibold text-lg mb-2">{video.title}</h3>
        <p className="text-sm text-gray-600 mb-3">{video.description}</p>

        <div className="flex justify-between items-center mb-4">
          <span className="text-sm font-medium">by {video.creator.name}</span>
          <span className="text-lg font-bold">
            Rp{video.price.toLocaleString()}
          </span>
        </div>

        {userId ? (
          <VideoPlayer
            video={video}
            isPurchased={!!isPurchased}
            isSubscribed={!!isSubscribed}
          />
        ) : (
          <Button className="w-full" disabled>
            Sign in to watch
          </Button>
        )}
      </div>
    </div>
  );
}
