"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { topUpSchema } from "@/lib/schemas";
import { useUserStore } from "@/lib/store/useUserStore";
import { useState } from "react";
import { z } from "zod";

interface TopUpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const TopUpDialog = ({ open, onOpenChange }: TopUpDialogProps) => {
  const [amount, setAmount] = useState("");
  const [error, setError] = useState("");
  const { addBalance } = useUserStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const validatedData = topUpSchema.parse({ amount: parseInt(amount) });

      // Simulate payment processing
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update balance in store
      addBalance(validatedData.amount);

      // Reset form and close dialog
      setAmount("");
      setError("");
      onOpenChange(false);
    } catch (err) {
      if (err instanceof z.ZodError) {
        setError(err.errors[0].message);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Top Up Balance</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="amount">Amount (Rp)</Label>
            <Input
              id="amount"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="Minimum Rp10,000"
              min="10000"
            />
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>

          <Button type="submit" className="w-full">
            Process Payment
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};
